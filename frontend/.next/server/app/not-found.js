/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXBkaW5lLWZyb250ZW5kLz9kYzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TenantContext.tsx */ \"(ssr)/./src/contexts/TenantContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmNvbnRleHRzJTJGVGVuYW50Q29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8/NTdiYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9mcm9udGVuZC9zcmMvY29udGV4dHMvVGVuYW50Q29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/**\n * Tenant context provider for multi-tenant authentication\n */ /* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction TenantProvider({ children, initialTenant = null }) {\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTenant);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mounted = true;\n        async function loadTenantAndUser() {\n            try {\n                setIsLoading(true);\n                setError(null);\n                // Get current session\n                const { data: { session }, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (sessionError) throw sessionError;\n                if (!session) {\n                    if (mounted) {\n                        setUser(null);\n                        setTenant(null);\n                        setIsLoading(false);\n                    }\n                    return;\n                }\n                // Get tenant slug from various sources\n                const tenantSlug = getTenantSlug();\n                if (!tenantSlug && !initialTenant) {\n                    // No tenant context available\n                    if (mounted) {\n                        setError(new Error(\"No tenant context available\"));\n                        setIsLoading(false);\n                    }\n                    return;\n                }\n                // Fetch user's tenant data\n                const { data: userData, error: userError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"users\").select(`\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          `).eq(\"id\", session.user.id).single();\n                if (userError) throw userError;\n                // Find the matching tenant\n                const userTenant = userData.user_tenants.find((ut)=>ut.tenant.slug === (tenantSlug || initialTenant?.slug));\n                if (!userTenant) {\n                    throw new Error(\"User does not have access to this tenant\");\n                }\n                if (mounted) {\n                    setUser({\n                        id: userData.id,\n                        email: userData.email,\n                        tenant_id: userTenant.tenant.id,\n                        role: userTenant.role,\n                        first_name: userData.first_name,\n                        last_name: userData.last_name,\n                        avatar_url: userData.avatar_url,\n                        created_at: userData.created_at,\n                        updated_at: userData.updated_at\n                    });\n                    setTenant(userTenant.tenant);\n                    setIsLoading(false);\n                }\n            } catch (err) {\n                console.error(\"Error loading tenant context:\", err);\n                if (mounted) {\n                    setError(err);\n                    setIsLoading(false);\n                }\n            }\n        }\n        loadTenantAndUser();\n        // Subscribe to auth changes\n        const { data: { subscription } } = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            if (event === \"SIGNED_IN\" || event === \"TOKEN_REFRESHED\") {\n                loadTenantAndUser();\n            } else if (event === \"SIGNED_OUT\") {\n                setUser(null);\n                setTenant(null);\n                router.push(\"/login\");\n            }\n        });\n        return ()=>{\n            mounted = false;\n            subscription.unsubscribe();\n        };\n    }, [\n        router,\n        initialTenant\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            user,\n            isLoading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Hook to use tenant context\n */ function useTenant() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (context === undefined) {\n        throw new Error(\"useTenant must be used within a TenantProvider\");\n    }\n    return context;\n}\n/**\n * Get tenant slug from various sources\n */ function getTenantSlug() {\n    if (true) return null;\n    // 1. Check subdomain\n    const hostname = window.location.hostname;\n    const subdomain = hostname.split(\".\")[0];\n    if (subdomain && subdomain !== \"www\" && subdomain !== \"localhost\") {\n        return subdomain;\n    }\n    // 2. Check URL path\n    const pathMatch = window.location.pathname.match(/^\\/t\\/([^\\/]+)/);\n    if (pathMatch) {\n        return pathMatch[1];\n    }\n    // 3. Check query parameter\n    const params = new URLSearchParams(window.location.search);\n    const tenantParam = params.get(\"tenant\");\n    if (tenantParam) {\n        return tenantParam;\n    }\n    // 4. Check cookie\n    const tenantCookie = document.cookie.split(\"; \").find((row)=>row.startsWith(\"tenant-slug=\"));\n    if (tenantCookie) {\n        return tenantCookie.split(\"=\")[1];\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TenantContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   getTenantSupabaseClient: () => (/* binding */ getTenantSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/**\n * Supabase client configuration with multi-tenant support\n */ \nconst supabaseUrl = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\";\n// Debug logging to verify configuration\nconsole.log(\"\\uD83D\\uDD0D Supabase Config Check:\");\nconsole.log(\"URL:\", supabaseUrl);\nconsole.log(\"Key:\", supabaseKey ? `${supabaseKey.substring(0, 20)}...` : \"NOT SET\");\nconst createClient = ()=>(0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseKey);\n// Legacy function for backward compatibility\nfunction getSupabaseClient() {\n    return createClient();\n}\n// Helper to get tenant-scoped Supabase client\nfunction getTenantSupabaseClient(tenantId) {\n    const client = getSupabaseClient();\n    // Add tenant context to all queries\n    client.auth.onAuthStateChange((event, session)=>{\n        if (session) {\n            // Set tenant context in headers for RLS\n            client.rest.headers[\"x-tenant-id\"] = tenantId;\n        }\n    });\n    return client;\n}\n// Export the default client\nconst supabase = getSupabaseClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"147f091b30a9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGJhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE0N2YwOTFiMzBhOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(rsc)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/**\n * Root layout with multi-tenant authentication\n */ \n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_1__.TenantProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7Q0FFQztBQUV5RDtBQUNuQztBQUVSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ0wsbUVBQWNBOzBCQUNaRTs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUm9vdCBsYXlvdXQgd2l0aCBtdWx0aS10ZW5hbnQgYXV0aGVudGljYXRpb25cbiAqL1xuXG5pbXBvcnQgeyBUZW5hbnRQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPFRlbmFudFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9UZW5hbnRQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59Il0sIm5hbWVzIjpbIlRlbmFudFByb3ZpZGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * 404 Not Found page\n */ \n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-8\",\n                            children: \"The page you're looking for doesn't exist or the tenant is not active.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                            children: \"Go to Home\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/login\",\n                                className: \"text-blue-600 hover:text-blue-500 text-sm font-medium\",\n                                children: \"Sign in to your account\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ e0),\n/* harmony export */   useTenant: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#TenantProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#useTenant`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/TenantContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();